import { Factory } from 'fishery'
import { faker } from '@faker-js/faker'

import { Company } from '@prisma/client'

export const companyFactory = Factory.define<Company>(({ associations }) => ({
    id: faker.string.uuid(),
    name: faker.company.name(),
    address: faker.location.streetAddress(),
    city: faker.location.city(),
    country: faker.location.country(),
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
    detailContent: faker.lorem.paragraph(),
    foundingYear: faker.date.past().getFullYear() || null,
    headerImageUrl: faker.image.urlPicsumPhotos() || null,
    logoImageUrl: faker.image.avatar() || null,
    totalEmployees: faker.number.int({ min: 10, max: 1000 }) || null,
    dynamicLink: faker.internet.url() || null,
    createdAt: faker.date.past(),
    updatedAt: faker.date.recent(),
    isFairManaged: faker.datatype.boolean(),
    companyUserId: faker.string.uuid() || null,
    stripeCustomerId: faker.string.uuid() || null,
}))
