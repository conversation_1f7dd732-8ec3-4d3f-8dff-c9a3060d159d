import { Module } from '@nestjs/common'
import { PricingPlanService } from './pricing-plan.service'
import { PricingPlanResolver } from './pricing-plan.resolver'
import { PrismaModule } from '../prisma.module'
import { StripeModule } from '@golevelup/nestjs-stripe'

@Module({
    imports: [PrismaModule, StripeModule],
    providers: [PricingPlanService, PricingPlanResolver],
    exports: [PricingPlanService],
})
export class PricingPlanModule {}
