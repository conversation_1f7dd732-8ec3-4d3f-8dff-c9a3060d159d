import {
    ObjectType,
    Field,
    ID,
    Float,
    Int,
    InputType,
    registerEnumType,
} from '@nestjs/graphql'
import { BillingPeriod, PricingPlanType } from '@prisma/client'

// Register enums for GraphQL
registerEnumType(BillingPeriod, {
    name: 'Billing<PERSON>eri<PERSON>',
    description: 'The billing period for a pricing plan',
})

registerEnumType(PricingPlanType, {
    name: 'PricingPlanType',
    description: 'The type of pricing plan',
})

@ObjectType()
export class PricingPlan {
    @Field(() => ID)
    id: string

    @Field()
    name: string

    @Field()
    displayName: string

    @Field({ nullable: true })
    description?: string

    @Field(() => Float)
    price: number

    @Field()
    currency: string

    @Field(() => BillingPeriod)
    billingPeriod: BillingPeriod

    @Field(() => Int)
    durationDays: number

    @Field()
    isPopular: boolean

    @Field()
    isCustom: boolean

    @Field(() => Int)
    displayOrder: number

    @Field()
    isActive: boolean

    @Field()
    unlimitedJobAdverts: boolean

    @Field()
    hasCustomBranding: boolean

    @Field(() => PricingPlanType)
    planType: PricingPlanType

    @Field({ nullable: true })
    stripeProductId?: string

    @Field({ nullable: true })
    stripePriceId?: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date
}

@InputType()
export class CreatePricingPlanInput {
    @Field()
    name: string

    @Field()
    displayName: string

    @Field({ nullable: true })
    description?: string

    @Field(() => Float)
    price: number

    @Field({ defaultValue: 'EUR' })
    currency: string

    @Field(() => BillingPeriod)
    billingPeriod: BillingPeriod

    @Field(() => Int)
    durationDays: number

    @Field({ defaultValue: false })
    isPopular: boolean

    @Field({ defaultValue: false })
    isCustom: boolean

    @Field(() => Int, { defaultValue: 0 })
    displayOrder: number

    @Field({ defaultValue: true })
    isActive: boolean

    @Field({ defaultValue: false })
    unlimitedJobAdverts: boolean

    @Field({ defaultValue: false })
    hasCustomBranding: boolean

    @Field(() => PricingPlanType, { defaultValue: PricingPlanType.STANDARD })
    planType: PricingPlanType
}

@InputType()
export class UpdatePricingPlanInput {
    @Field({ nullable: false })
    id: string

    @Field({ nullable: true })
    name?: string

    @Field({ nullable: true })
    displayName?: string

    @Field({ nullable: true })
    description?: string

    @Field(() => Float, { nullable: true })
    price?: number

    @Field({ nullable: true })
    currency?: string

    @Field(() => BillingPeriod, { nullable: true })
    billingPeriod?: BillingPeriod

    @Field(() => Int, { nullable: true })
    durationDays?: number

    @Field({ nullable: true })
    isPopular?: boolean

    @Field({ nullable: true })
    isCustom?: boolean

    @Field(() => Int, { nullable: true })
    displayOrder?: number

    @Field({ nullable: true })
    isActive?: boolean

    @Field({ nullable: true })
    unlimitedJobAdverts?: boolean

    @Field({ nullable: true })
    hasCustomBranding?: boolean

    @Field(() => PricingPlanType, { nullable: true })
    planType?: PricingPlanType
}
