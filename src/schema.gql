# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

enum ActionState {
  BOOKMARKED
  DELETED
  DISLIKED
  LIKED
  MATCHED
}

type Address {
  city: String
  country: String
  line1: String
  line2: String
  postal_code: String
  state: String
}

type Agora {
  token: String!
  uid: String!
}

type Applicant {
  applicantDocuments: [ApplicantDocument!]
  availableFrom: DateTime
  birthDate: DateTime
  city: String
  description: String
  deviceTokens: [DeviceToken!]
  environment: Float
  firstName: String
  graduation: String
  id: String!
  jobAction: [JobAction!]
  jobsFilter: JobsFilter
  lastActive: DateTime
  lastName: String
  personality: Float
  phoneNumber: String
  profileImageUrl: String
  receiveNotifications: Boolean
  schoolName: String
  strengths: [String!]
  subjects: [String!]
  userId: String
  weaknesses: [String!]
}

type ApplicantDocument {
  applicant: Applicant!
  documentPreviewUrl: String!

  """ID"""
  id: String!
  name: String!
  storage: String!
  url: String!
}

type Appointment {
  applicant: Applicant!
  applicantId: String!
  applicantIsNew: Boolean!
  chatRoom: ChatRoom!
  chatRoomId: String!
  companyIsNew: Boolean!
  contactPersonTimeslot: ContactPersonTimeslot!
  contactPersonTimeslotId: String!
  id: String!
  rejectReason: String
  reservationDate: DateTime!
  status: AppointmentStatus!
}

input AppointmentFilterInput {
  applicantId: String
  companyId: String
  contactPersonId: String
  date: DateTime
  fairId: String
  status: AppointmentStatus
  timeRange: TimeRangeInput
}

type AppointmentPaginated {
  count: Float
  items: [Appointment!]!
}

enum AppointmentStatus {
  CANCELED
  CONFIRMED
  REJECTED
  REQUESTED
}

type BillingDetails {
  address: Address
  email: String
  name: String
  phone: String
}

"""The billing period for a pricing plan"""
enum BillingPeriod {
  ANNUAL
  CUSTOM
  MONTHLY
  ONE_TIME
  QUARTERLY
  SEMI_ANNUAL
}

input BulkDeleteContactPersonTimeslotInput {
  ids: [String!]!
}

type BulkDeleteResponse {
  deletedCount: Int!
  deletedIds: [String!]!
}

type CancelSubscriptionResponseDto {
  application_fee_percent: Float!
  cancel_at_period_end: Boolean!
  canceled_at: Float!
  created: Float!
  current_period_end: Float!
  current_period_start: Float!
  customer: String!
  ended_at: Float!
  id: String!
  object: String!
  start: Float!
  status: String!
}

type Card {
  brand: String
  country: String
  exp_month: Float
  exp_year: Float
  fingerprint: String!
  funding: String
  generated_from: String
  last4: String
  wallet: String
}

type Charges {
  data: [String!]!
  has_more: Boolean!
  object: String!
  total_count: Float!
  url: String!
}

type ChatRoom {
  appointment: Appointment
  companyUsers: [CompanyUser!]
  id: String!
  jobAction: JobAction
  messages: [Message!]!
  status: String
}

input ChatRoomCriteriaInput {
  appointmentId: String
  jobActionId: String
}

type ClaimsResponse {
  claims: FirebaseUserDto
  error: ErrorType
}

type Company {
  address: String!
  city: String!
  companyUserId: String
  companyUsers: [CompanyUser!]
  country: String!
  createdAt: DateTime!
  detailContent: String
  dynamicLink: String
  foundingYear: Float
  headerImageUrl: String
  id: String!
  isFairManaged: Boolean
  isInFair: Boolean
  jobAdvert: [JobAdvert!]
  latitude: Float
  logoImageUrl: String
  longitude: Float
  name: String!
  stripeCustomerId: String
  totalEmployees: Float
  updatedAt: DateTime!
}

type CompanyFairContactPerson {
  ContactPersonTimeslot: [ContactPersonTimeslot!]!
  companyFairParticipation: CompanyFairParticipation!
  companyFairParticipationId: String!
  contactPerson: ContactPerson!
  contactPersonId: String!
  id: String!
}

type CompanyFairJob {
  companyFairParticipation: CompanyFairParticipation!
  companyFairParticipationId: String!
  description: String
  fairJob: FairJob!
  fairJobId: String!
  id: String!
}

type CompanyFairParticipation {
  categories: [JobCategory!]
  company: Company!
  companyFairContactPersons: [CompanyFairContactPerson!]
  companyFairJobs: [CompanyFairJob!]
  companyId: String!
  fair: Fair!
  fairId: String!
  id: String!
  partnerLinks: [PartnerLink!]
}

type CompanyUser {
  activeCompanyId: String
  avatarImageUrl: String
  companies: [Company!]!
  createdAt: DateTime!
  email: String

  """Company User ID"""
  id: String!
  jobAdverts: [JobAdvert!]!
  name: String
  updatedAt: DateTime!
  user: User!
  userId: String!
  userRights: [UserRight!]!
}

input ConfirmPaymentIntentInput {
  """Payment Intent ID"""
  paymentIntentId: String!

  """Payment Method ID"""
  paymentMethodId: String!
}

type ContactPerson {
  company: Company!
  companyFairContactPersons: [CompanyFairContactPerson!]!
  companyId: String!
  email: String
  id: String!
  name: String!
  phone: String
  position: String
}

type ContactPersonTimeslot {
  Appointment: Appointment!
  available: Boolean!
  companyFairContactPerson: CompanyFairContactPerson!
  companyFairContactPersonId: String!
  endTime: DateTime!
  id: String!
  startTime: DateTime!
}

input CreateAgoraInput {
  jobActionId: String!
}

input CreateApplicantDocumentInput {
  documentPreviewUrl: String!
  name: String!
  storage: String!
  url: String!
}

input CreateApplicantInput {
  availableFrom: DateTime
  birthDate: DateTime
  city: String
  description: String
  environment: Int
  firstName: String
  graduation: String
  lastActive: String
  lastName: String
  personality: Int
  profileImageUrl: String
  receiveNotifications: Boolean
  schoolName: String
  strengths: [String!]
  subjects: [String!]
  weaknesses: [String!]
}

input CreateAppointmentInput {
  applicantId: String
  companyIsNew: Boolean
  contactPersonTimeslotId: String!
  rejectReason: String
  status: AppointmentStatus
}

input CreateChatRoomInput {
  status: String
}

input CreateCheckoutSessionInput {
  """Advert ID from frontend"""
  advertId: String!

  """Advert Title ID from frontend"""
  advertTitle: String!

  """User email from frontend"""
  email: String!

  """Price ID from frontend"""
  priceId: String!
}

input CreateCompanyDynamicLinkInput {
  companyId: String!
}

input CreateCompanyFairContactPersonInput {
  companyFairParticipationId: String!
  contactPersonId: String!
}

input CreateCompanyFairJobInput {
  companyFairParticipationId: String!
  description: String
  fairJobId: String!
}

input CreateCompanyFairParticipationInput {
  categoryIds: [String!]!
  companyId: String!
  fairId: String!
  partnerLinkIds: [String!]
}

input CreateCompanyInput {
  address: String!
  city: String!
  companyUserId: String
  country: String!
  detailContent: String
  dynamicLink: String
  foundingYear: Float
  headerImageUrl: String
  isFairManaged: Boolean
  latitude: Float
  logoImageUrl: String
  longitude: Float
  name: String!
  stripeCustomerId: String
  totalEmployees: Float
}

input CreateCompanyUserInput {
  activeCompanyId: String
  avatarImageUrl: String
  email: String!
  name: String
}

input CreateContactPersonInput {
  companyId: String!
  email: String
  name: String!
  phone: String
  position: String
}

input CreateContactPersonTimeslotInput {
  available: Boolean
  companyFairContactPersonId: String!
  endTime: DateTime!
  startTime: DateTime!
}

input CreateDeviceTokenInput {
  token: String!
}

input CreateEmailNotificationInput {
  emailBody: String
  emailSubject: String
}

input CreateFairDayInput {
  day: DateTime!
  endTime: DateTime!
  fairId: String
  startTime: DateTime!
}

input CreateFairInput {
  city: String!
  contactPersonEmail: String
  contactPersonName: String
  description: String
  endDate: DateTime!
  location: String!
  locationName: String
  logoImageUrl: String
  name: String!
  publisherLogoImageUrl: String
  publisherName: String
  registrationEndDate: DateTime!
  registrationStartDate: DateTime!
  startDate: DateTime!
  status: String
}

input CreateFairJobInput {
  title: String!
}

input CreateFgadminInput {
  avatarImageUrl: String
  email: String!
  name: String
  type: String
}

input CreateJobActionHistoryInput {
  newState: String!
  prevState: String!
  type: String!
}

input CreateJobActionInput {
  applicantIsNew: Boolean
  companyIsNew: Boolean
  declineReason: String
  deletedFromApplicant: Boolean
  deletedFromCompany: Boolean
  state: ActionState!
  status: String
}

input CreateJobAdDynamicLinkInput {
  jobAdvertId: String!
}

input CreateJobAdvertInput {
  activeFromDate: DateTime
  address: String
  approved: Boolean
  city: String!
  companyName: String
  companyUserId: String
  declineReason: String
  description: String!
  detailDescription: String!
  district: String!
  educationDuration: Float
  endDate: DateTime
  gehalt: [Float!]!
  headerImageUrl: String
  holidayDays: Float
  imageUrl: String
  impressions: Float
  isDeclined: Boolean
  isDeleted: Boolean
  isDraft: Boolean
  jobCategoryId: String
  latitude: Float
  longitude: Float
  startDate: DateTime!
  title: String!
  type: String!
  workHours: Float
}

input CreateJobCategoryInput {
  imageUrl: String!
  name: String!
}

input CreateJobsFilterInput {
  categories: [String!]!
  currentLocation: String
  latitude: Float
  longitude: Float
  radius: Float
  type: [JobAdvertType!]
}

input CreateMessageInput {
  authorName: String!
  content: String!
  deletedAt: DateTime
  deletedBy: String
  deletedById: String
  isApplicant: Boolean = false
  isCompany: Boolean
  isDeleted: Boolean
  isDelivered: Boolean
  isSeen: Boolean
  isSent: Boolean
}

input CreateNotificationInput {
  isNew: Boolean
  type: String
}

input CreatePartnerLinkInput {
  name: String!
  url: String!
}

input CreatePaymentIntentInput {
  """Payment amount"""
  amount: Float!

  """Payment currency"""
  currency: String

  """Payment customer"""
  customer: String
}

input CreatePortalSessionInput {
  """Stripe Customer ID from frontend"""
  customerId: String!
}

input CreatePricingPlanInput {
  billingPeriod: BillingPeriod!
  currency: String! = "EUR"
  description: String
  displayName: String!
  displayOrder: Int! = 0
  durationDays: Int!
  hasCustomBranding: Boolean! = false
  isActive: Boolean! = true
  isCustom: Boolean! = false
  isPopular: Boolean! = false
  name: String!
  planType: PricingPlanType! = STANDARD
  price: Float!
  unlimitedJobAdverts: Boolean! = false
}

input CreatePushNotificationInput {
  actionUrl: String
  body: String
  title: String
}

type CreateSessionResponseDto {
  url: String!
}

input CreateStaleImageInput {
  url: String!
}

type CreateStripeCustomerResponseDto {
  address: String
  balance: Float
  created: DateTime
  currency: String
  default_source: String
  delinquent: Boolean
  description: String
  discount: String
  email: String!
  id: String!
  name: String
  object: String
  phone: String
}

input CreateStripeSubscriptionInput {
  advertTitle: String
  companyId: String!
  couponId: String
  customerId: String
  jobAdvertId: String
  jobAdverts: [String!]
  percent_off: Float
  priceId: String!
  promoCode: String
}

type CreateStripeSubscriptionResponseDto {
  cancel_at_period_end: Boolean
  hosted_invoice_url: String
  id: String
  invoice_pdf: String
  latest_invoice: StripeInvoiceResponseDto!
}

input CreateSubscriptionInput {
  amountTotal: Float!
  checkoutSessionId: String!
  currency: String!
  expiresAt: DateTime!
  invoiceId: String!
  isActive: Boolean! = false
  paymentStatus: String!
  percent_off: Float
  plan: String!
  status: String
  stripeCustomerId: String!
  stripeSubscriptionId: String!
}

input CreateSuperUserInput {
  avatarImageUrl: String
  email: String!
  name: String
}

input CreateTimeslotInput {
  endTime: DateTime!
  fairId: String!
  startTime: DateTime!
}

input CreateUpdateJobActionInput {
  applicantId: String
  applicantIsNew: Boolean
  companyIsNew: Boolean
  declineReason: String
  deletedFromApplicant: Boolean
  deletedFromCompany: Boolean
  id: String
  jobAdvertId: String
  state: ActionState
  status: String
}

input CreateUserRightInput {
  companyAdmin: Boolean
  createJobAd: Boolean
  createUser: Boolean
  deleteJobAd: Boolean
  editCompany: Boolean
  editJobAd: Boolean
  superAdmin: Boolean
  updateJobAd: Boolean
  viewApplicants: Boolean
  viewJobAd: Boolean
}

input CustomerIdInput {
  """Stripe Customer ID"""
  customerId: String!
}

type DataObj {
  billing_details: BillingDetails
  card: Card
  created: Float
  customer: String!
  id: String
  livemode: Boolean
  object: String
  redaction: String
  sepa_debit: Sepa
  type: String!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type DeviceToken {
  applicant: Applicant!
  id: String!
  token: String
}

type DynamicLink {
  """Dynamic Link"""
  url: String!
}

type EmailNotification {
  createdAt: DateTime!
  emailBody: String
  emailSubject: String
  id: String!
  updatedAt: DateTime!
}

type ErrorType {
  code: String
  message: String!
}

type Fair {
  city: String!
  companyFairParticipations: [CompanyFairParticipation!]!
  contactPersonEmail: String
  contactPersonName: String
  description: String
  endDate: DateTime!
  fairDays: [FairDay!]
  id: String!
  location: String!
  locationName: String
  logoImageUrl: String
  name: String!
  publisherLogoImageUrl: String
  publisherName: String
  registrationEndDate: DateTime!
  registrationStartDate: DateTime!
  standardTimeslots: [Timeslot!]!
  startDate: DateTime!
  status: FairStatus
}

type FairDay {
  createdAt: DateTime!
  day: DateTime!
  endTime: DateTime!
  fair: Fair!
  fairId: String!
  id: String!
  startTime: DateTime!
  updatedAt: DateTime!
}

type FairJob {
  companyFairJobs: [CompanyFairJob!]!
  id: String!
  title: String!
}

type FairStats {
  appointments: Int!
  chatRooms: Int!
  companies: Int!
  fairs: Int!
}

enum FairStatus {
  ACTIVE
  INACTIVE
}

type Fgadmin {
  avatarImageUrl: String
  createdAt: DateTime!
  email: String
  id: String!
  name: String
  type: String
  updatedAt: DateTime!
  user: User!
  userId: String!
}

type FilterOptionsResponse {
  applicants: [IdTextPair!]!
  companies: [IdTextPair!]!
  contactPersons: [IdTextPair!]!
  fairs: [IdTextPair!]!
}

input FirebaseCreateUserDto {
  avatarImageUrl: String
  disabled: Boolean
  displayName: String
  email: String!
  emailVerified: Boolean
  password: String!
  phoneNumber: String
}

type FirebaseUserDto {
  applicantId: String
  aud: String
  auth_time: Float
  bridgeUserId: String
  companyId: String
  companyUserId: String
  createJobAd: Boolean
  createUser: Boolean
  editCompany: Boolean
  email: String
  email_verified: Boolean
  exp: Float
  iat: Float
  iss: String
  name: String
  sub: String
  superAdmin: Boolean
  uid: String
  user_id: String
}

type IdTextPair {
  id: String!
  text: String!
}

input InvoiceIdInput {
  """Stripe Invoice ID"""
  invoiceId: String!
}

type Issuer {
  type: String!
}

type JobAction {
  applicant: Applicant!
  applicantIsNew: Boolean
  chatRoomId: String
  companyIsNew: Boolean
  declineReason: String
  deletedFromApplicant: Boolean
  deletedFromCompany: Boolean
  id: String!
  jobAdvert: JobAdvert!
  state: ActionState!
  status: String
}

type JobActionHistory {
  createdAt: DateTime
  id: String!
  jobAction: JobAction!
  newState: String
  prevState: String
  updatedAt: DateTime
}

type JobAdvert {
  _count: JobAdvertStats
  activeFromDate: DateTime
  address: String
  approved: Boolean
  categories: [JobCategory!]
  city: String
  company: Company!
  companyId: String
  companyUserId: String
  createdAt: DateTime
  declineReason: String
  description: String
  detailDescription: String
  district: String
  dynamicLink: String
  educationDuration: Float
  gehalt: [Float!]
  headerImageUrl: String
  holidayDays: Float
  id: String!
  isDeclined: Boolean
  isDeleted: Boolean
  isDraft: Boolean
  jobAction: [JobAction!]
  latitude: Float
  longitude: Float
  paused: Boolean
  responsibleUsers: [CompanyUser!]
  startDate: DateTime
  status: String
  subscriptions: [StripeSubscription!]
  title: String!
  type: String
  updatedAt: DateTime
  workHours: Float
}

type JobAdvertCount {
  jobAdverts: Int!
}

input JobAdvertFilterOptionsInput {
  categoryId: String
  latitude: Float
  longitude: Float
  type: [JobAdvertType!]
}

type JobAdvertStats {
  bookmarks: Int!
  impressions: Int!
  likes: Int!
  matches: Int!
}

enum JobAdvertType {
  AUSBILDUNG
  PRAKTIKUM
}

type JobCategory {
  _count: JobAdvertCount!
  id: String!
  imageUrl: String!
  jobAdverts: [JobAdvert!]!
  jobsFilter: [JobsFilter!]!
  name: String!
}

type JobsFilter {
  applicant: Applicant!
  categories: [JobCategory!]!
  currentLocation: String
  id: String!
  latitude: Float
  longitude: Float
  radius: Float
  type: [JobAdvertType!]!
}

type Lines {
  has_more: Boolean!
  object: String!
  total_count: Float!
  url: String!
}

input LoginRequestDto {
  token: String
}

type LoginResponse {
  error: ErrorType
  user: FirebaseUserDto
}

input MarkMessagesAsSeenInput {
  applicantId: String!
  chatRoomId: String!
}

type MarkMessagesAsSeenResponse {
  count: Float!
}

type Message {
  authorName: String
  content: String!
  createdAt: DateTime!
  deletedAt: DateTime
  deletedBy: String
  deletedById: String
  id: String!
  isApplicant: Boolean
  isCompany: Boolean
  isDeleted: Boolean
  isDelivered: Boolean
  isSeen: Boolean
  isSent: Boolean
}

type Mutation {
  approveJobAdvert(jobAdId: String!, superUserId: String): JobAdvert!
  blockJobAdvert(declineReason: String!, jobAdId: String!, superUserId: String): JobAdvert!
  bulkDeleteContactPersonTimeslots(input: BulkDeleteContactPersonTimeslotInput!): BulkDeleteResponse!
  cancelSubscription(subscriptionIdInput: SubscriptionIdInput!): CancelSubscriptionResponseDto!
  cloneFair(cloneFairInput: CreateFairInput!, id: String!): Fair!
  confirmPaymentIntent(confirmPaymentIntentInput: ConfirmPaymentIntentInput!): PaymentIntentResponseDto!
  createApplicant(createApplicantInput: CreateApplicantInput): Applicant!
  createApplicantDocument(applicantId: String, createApplicantDocumentInput: CreateApplicantDocumentInput!): ApplicantDocument!
  createAppointment(createAppointmentInput: CreateAppointmentInput!): Appointment!
  createBulkCheckoutSession(companyId: String, createCheckoutSessionInput: [CreateCheckoutSessionInput!]!, customer: String): CreateSessionResponseDto!
  createChatRoom(companyUserId: String, createChatRoomInput: CreateChatRoomInput!, jobActionId: String!): ChatRoom!
  createCheckoutSession(companyId: String, createCheckoutSessionInput: CreateCheckoutSessionInput!, customer: String): CreateSessionResponseDto!
  createCompany(companyInput: CreateCompanyInput!): Company!
  createCompanyByAdmin(companyInput: CreateCompanyInput!): Company!
  createCompanyDynamicLink(createDynamicLinkInput: CreateCompanyDynamicLinkInput!): DynamicLink!
  createCompanyFairContactPerson(createCompanyFairContactPersonInput: CreateCompanyFairContactPersonInput!): CompanyFairContactPerson!
  createCompanyFairJob(createCompanyFairJobInput: CreateCompanyFairJobInput!): CompanyFairJob!
  createCompanyFairParticipation(createCompanyFairParticipationInput: CreateCompanyFairParticipationInput!): CompanyFairParticipation!
  createCompanyUser(companyId: String, createCompanyUserInput: CreateCompanyUserInput!, userId: String!): CompanyUser!
  createContactPerson(createContactPersonInput: CreateContactPersonInput!): ContactPerson!
  createContactPersonTimeslot(createContactPersonTimeslotInput: CreateContactPersonTimeslotInput!): ContactPersonTimeslot!
  createDeviceToken(applicantId: String!, createDeviceTokenInput: CreateDeviceTokenInput!): DeviceToken!
  createFair(createFairInput: CreateFairInput!, fairDays: [CreateFairDayInput!]!): Fair!
  createFairDay(createFairDayInput: CreateFairDayInput!): FairDay!
  createFairJob(createFairJobInput: CreateFairJobInput!): FairJob!
  createFgadmin(createFgadminInput: CreateFgadminInput!, id: String!): Fgadmin!
  createFirebaseBridgeUser(companyId: String, firebaseUser: FirebaseCreateUserDto!, userRights: CreateUserRightInput): CompanyUser!
  createJobAction(applicantId: String, createJobActionInput: CreateJobActionInput!, jobAdvertId: String!): JobAction!
  createJobActionHistory(createJobActionHistoryInput: CreateJobActionHistoryInput!, jobActionId: String!): JobActionHistory!
  createJobAd(categoryIds: [String!]!, companyId: String!, createJobAdvertInput: CreateJobAdvertInput!, responsibleUsersIds: [String!]): JobAdvert!
  createJobAdDynamicLink(createDynamicLinkInput: CreateJobAdDynamicLinkInput!): DynamicLink!
  createJobCategory(createJobCategoryInput: CreateJobCategoryInput!): JobCategory!
  createJobsFilter(applicantId: String, createJobsFilterInput: CreateJobsFilterInput!): JobsFilter!
  createMessage(applicantId: String, chatRoomId: String!, companyFairContactPersonId: String, companyUserId: String, createMessageInput: CreateMessageInput!): Message!
  createNotification(applicantId: String, companyUserId: String, createEmailNotificationInput: CreateEmailNotificationInput, createNotificationInput: CreateNotificationInput!, createPushNotificationInput: CreatePushNotificationInput): Notification!
  createPartnerLink(createPartnerLinkInput: CreatePartnerLinkInput!): PartnerLink!
  createPaymentIntent(createPaymentIntentInput: CreatePaymentIntentInput!): PaymentIntentResponseDto!
  createPortalSession(createCheckoutSessionInput: CreatePortalSessionInput!): CreateSessionResponseDto!
  createPricingPlan(input: CreatePricingPlanInput!): PricingPlan!
  createSetupCheckoutDetails(companyId: String, customer: String, type: String = "checkout"): CreateSessionResponseDto!
  createStaleImage(createStaleImageInput: CreateStaleImageInput!): StaleImage!
  createStripeCustomer(companyId: String, createStripeCustomerInput: StripeCustomerInput!): CreateStripeCustomerResponseDto!
  createStripeSubscription(createStripeSubscriptionInput: CreateStripeSubscriptionInput!, subType: String): CreateStripeSubscriptionResponseDto!
  createSubscription(companyId: String, createSubscriptionInput: CreateSubscriptionInput!, jobAdvertId: String!): StripeSubscription!
  createSuperUser(createSuperUserInput: CreateSuperUserInput!, userId: String!): SuperUser!
  createTimeslot(createTimeslotInput: CreateTimeslotInput!): Timeslot!
  createUserRights(companyId: String!, companyUserId: String!, createUserRightInput: CreateUserRightInput!): UserRight!
  deactivatePricingPlan(id: ID!): PricingPlan!
  declineApplicant(declineReason: String, jobActionId: String!): JobAction!
  declineJobAction(jobActionId: String!): JobAction!
  deleteImage(imageUrl: String!): Boolean!
  deleteNotification(notificationId: String!): String!
  generateSubscriptionInvoice(companyId: String, createStripeSubscriptionInput: CreateStripeSubscriptionInput!, customerDetails: UpdateStripeCustomerInput): CreateStripeSubscriptionResponseDto!
  login(loginInput: LoginRequestDto!): LoginResponse!
  manageFair(id: String, isFairManaged: Boolean!): Company!
  markAllAsRead(applicantId: String, companyUserId: String): String!
  markAsRead(notificationId: String!): Notification!
  markMessagesAsSeen(markMessagesAsSeenInput: MarkMessagesAsSeenInput!): MarkMessagesAsSeenResponse!
  pauseJobAdvert(id: String!): JobAdvert!
  register(registerInput: RegisterDto): RegisterResponse!
  registerApplicant: RegisterResponse!
  removeAllStaleImages: Int!
  removeApplicant(id: String): Applicant!
  removeApplicantDocument(id: String!): ApplicantDocument!
  removeAppointment(id: String!, rejectReason: String): Appointment!
  removeChatRoom(id: String!): ChatRoom!
  removeCompany(id: String!): Company!
  removeCompanyFairContactPerson(id: String!): CompanyFairContactPerson!
  removeCompanyFairJob(id: String!): CompanyFairJob!
  removeCompanyFairParticipation(id: String!): CompanyFairParticipation!
  removeCompanyFromFair(companyId: String!, fairId: String!): CompanyFairParticipation!
  removeCompanyUser(id: String!): CompanyUser!
  removeContactPerson(id: String!): ContactPerson!
  removeContactPersonTimeslot(id: String!): ContactPersonTimeslot!
  removeDeviceToken(token: String): DeviceToken!
  removeDeviceTokensByApplicantId(applicantId: String): DeviceToken!
  removeFair(id: String!): Fair!
  removeFairDay(id: String!): FairDay!
  removeFairJob(id: String!): FairJob!
  removeFgadmin(id: String!): Fgadmin!
  removeJobAdvert(id: String!): JobAdvert!
  removeJobCategory(id: String!): JobCategory!
  removeJobsFilter(id: String!): JobsFilter!
  removeMultipleStaleImages(ids: [String!]!): Int!
  removeMultipleTimeslots(ids: [String!]!): Int!
  removePartnerLink(id: String!): PartnerLink!
  removeTimeslot(id: String!): Timeslot!
  resetPassword(email: String!): String!
  resumeJobAdvert(id: String!): JobAdvert!
  resumeSubscription(subscriptionIdInput: SubscriptionIdInput!): CancelSubscriptionResponseDto!
  retrieveStripeCoupon(promoCode: String!): StripeCouponResponseDto!
  retrieveStripePromoCode(promoCode: String!): StripePromoCodeResponseDto!
  retrieveSubscriptionInvoice(invoiceIdInput: InvoiceIdInput!): StripeInvoiceResponseDto!
  sendEmailNotification(companyUserId: String, createEmailNotificationInput: CreateEmailNotificationInput, createNotificationInput: CreateNotificationInput!): Notification!
  sendPushNotification(applicantId: String, companyUserId: String, createEmailNotificationInput: CreateEmailNotificationInput, createNotificationInput: CreateNotificationInput!, createPushNotificationInput: CreatePushNotificationInput): Notification!
  sendSubscriptionInvoice(invoiceIdInput: InvoiceIdInput!): StripeInvoiceResponseDto!
  setApplicant(createApplicantInput: CreateApplicantInput): Applicant!
  setAppointmentAsOld(appointmentId: String!): Appointment!
  setCustomUserClaims: ClaimsResponse!
  setDeviceToken(applicantId: String, setDeviceTokenInput: CreateDeviceTokenInput!): DeviceToken!
  setJobAction(createOrUpdateJobActionInput: CreateUpdateJobActionInput!): JobAction!
  setJobsFilter(applicantId: String, createJobsFilterInput: CreateJobsFilterInput!): JobsFilter!
  subscribeToPremium(createStripeSubscriptionInput: CreateStripeSubscriptionInput!, paymentMethodId: String): PaymentIntentResponseDto!
  updateApplicant(applicantId: String, updateApplicantInput: UpdateApplicantInput!): Applicant!
  updateApplicantDocument(updateApplicantDocumentInput: UpdateApplicantDocumentInput!): ApplicantDocument!
  updateAppointment(updateAppointmentInput: UpdateAppointmentInput!): Appointment!
  updateChatRoom(updateChatRoomInput: UpdateChatRoomInput!): ChatRoom!
  updateCompany(id: String, updateCompanyInput: UpdateCompanyInput!): Company!
  updateCompanyFairContactPerson(updateCompanyFairContactPersonInput: UpdateCompanyFairContactPersonInput!): CompanyFairContactPerson!
  updateCompanyFairJob(updateCompanyFairJobInput: UpdateCompanyFairJobInput!): CompanyFairJob!
  updateCompanyFairParticipation(updateCompanyFairParticipationInput: UpdateCompanyFairParticipationInput!): CompanyFairParticipation!
  updateCompanyUser(updateCompanyUserInput: UpdateCompanyUserInput!, userRights: CreateUserRightInput): CompanyUser!
  updateContactPerson(updateContactPersonInput: UpdateContactPersonInput!): ContactPerson!
  updateContactPersonTimeslot(updateContactPersonTimeslotInput: UpdateContactPersonTimeslotInput!): ContactPersonTimeslot!
  updateDeviceToken(updateDeviceTokenInput: UpdateDeviceTokenInput!): DeviceToken!
  updateFair(id: String!, updateFairInput: UpdateFairInput!): Fair!
  updateFairDay(updateFairDayInput: UpdateFairDayInput!): FairDay!
  updateFairJob(updateFairJobInput: UpdateFairJobInput!): FairJob!
  updateFgadmin(updateFgadminInput: UpdateFgadminInput!): Fgadmin!
  updateJobAction(currentState: String, updateJobActionInput: UpdateJobActionInput!): JobAction!
  updateJobAdvert(categoryIdsToConnect: [String!], categoryIdsToDisconnect: [String!], companyId: String!, id: String!, responsibleUsersIdsToConnect: [String!], responsibleUsersIdsToDisconnect: [String!], updateJobAdvertInput: UpdateJobAdvertInput!): JobAdvert!
  updateJobCategory(updateJobCategoryInput: UpdateJobCategoryInput!): JobCategory!
  updateJobsFilter(updateJobsFilterInput: UpdateJobsFilterInput!): JobsFilter!
  updateMessage(applicantId: String!, chatRoomId: String!, companyUserId: String!, id: String!, updateMessageInput: UpdateMessageInput!): Message!
  updatePartnerLink(updatePartnerLinkInput: UpdatePartnerLinkInput!): PartnerLink!
  updatePricingPlan(input: UpdatePricingPlanInput!): PricingPlan!
  updateStripeCustomer(createStripeCustomerInput: StripeCustomerInput!, customerId: String): CreateStripeCustomerResponseDto!
  updateSubscription(updateSubscriptionInput: UpdateSubscriptionInput!): StripeSubscription!
  updateTimeslot(updateTimeslotInput: UpdateTimeslotInput!): Timeslot!
  updateUserRights(companyId: String!, companyUserId: String!, id: String!, updateUserRightInput: UpdateUserRightInput!): UserRight!
  verifyCompanyUser(email: String!): CompanyUser!
  verifyFGAdmin(email: String!): Fgadmin!
  verifySuperUser(email: String!): SuperUser!
}

input NewLikeInput {
  applicantName: String!
  email: String!
  jobAdId: String!
  jobAdTitle: String!
}

type Notification {
  applicant: Applicant
  companyUser: CompanyUser
  createdAt: DateTime!
  emailNotification: EmailNotification
  id: String!
  isNew: Boolean
  pushNotification: PushNotification
  updatedAt: DateTime!
}

type PaginatedApplicantsResponse {
  items: [Applicant!]!
  meta: PaginationMeta!
}

type PaginatedCompaniesResponse {
  items: [Company!]!
  meta: PaginationMeta!
}

type PaginatedJobAdvertsResponse {
  items: [JobAdvert!]!
  meta: PaginationMeta!
}

input PaginationInput {
  limit: Int = 10
  page: Int = 1
  search: String
  status: String
}

type PaginationMeta {
  currentPage: Int!
  itemCount: Int!
  itemsPerPage: Int!
  totalItems: Int!
  totalPages: Int!
}

type PartnerLink {
  companyFairParticipations: [CompanyFairParticipation!]
  createdAt: DateTime!
  id: String!
  name: String!
  updatedAt: DateTime!
  url: String!
}

type PaymentIntentResponseDto {
  allowed_source_types: [String!]
  amount: Float!
  amount_capturable: Float
  amount_received: Float
  application: String
  application_fee_amount: Float
  canceled_at: Float
  cancellation_reason: String
  capture_method: String!
  charges: Charges!
  client_secret: String!
  confirmation_method: String
  created: Float!
  currency: String!
  customer: String
  description: String
  id: String!
  invoice: String
  last_payment_error: String
  livemode: Boolean!
  metadata: String
  next_action: String
  next_source_action: String
  object: String!
  on_behalf_of: String
  payment_method: String
  payment_method_options: PaymentMethodOptions!
  payment_method_types: [String!]!
  receipt_email: String
  review: String
  setup_future_usage: String
  shipping: String
  source: String
  statement_descriptor: String
  statement_descriptor_suffix: String
  status: String!
  transfer_data: String
  transfer_group: String
}

type PaymentMethodOptions {
  card: PaymentMethodOptionsCard!
}

type PaymentMethodOptionsCard {
  installments: String
  network: String
  request_three_d_secure: String
}

type PaymentMethodResponseDto {
  data: [DataObj!]
  id: String
  object: String
}

type PricingPlan {
  billingPeriod: BillingPeriod!
  createdAt: DateTime!
  currency: String!
  description: String
  displayName: String!
  displayOrder: Int!
  durationDays: Int!
  hasCustomBranding: Boolean!
  id: ID!
  isActive: Boolean!
  isCustom: Boolean!
  isPopular: Boolean!
  name: String!
  planType: PricingPlanType!
  price: Float!
  stripePriceId: String
  stripeProductId: String
  unlimitedJobAdverts: Boolean!
  updatedAt: DateTime!
}

"""The type of pricing plan"""
enum PricingPlanType {
  CUSTOM
  ENTERPRISE
  PROMOTIONAL
  STANDARD
}

type PushNotification {
  body: String
  createdAt: DateTime!
  id: String!
  title: String
  updatedAt: DateTime!
}

type Query {
  about: String!
  activePricingPlans: [PricingPlan!]!
  allApplicantDocuments: [ApplicantDocument!]!
  allApplicants: [Applicant!]!
  allChatMessages: [Message!]!
  allChatRooms: [ChatRoom!]!
  allCompanies: [Company!]!
  allCompaniesWithFairStatus(fairId: String!): [Company!]!
  allCompanyFairParticipation: [CompanyFairParticipation!]!
  allCompanyUsers: [CompanyUser!]!
  allContactPersonTimeslots: [ContactPersonTimeslot!]!
  allContactPersons: [ContactPerson!]!
  allDeviceToken: [DeviceToken!]!
  allFairDay: [FairDay!]!
  allFairJobs: [FairJob!]!
  allFairs: [Fair!]!
  allFgadmins: [Fgadmin!]!
  allJobActions: [JobAction!]!
  allJobAds: [JobAdvert!]!
  allJobCategories: [JobCategory!]!
  allJobsFilter: [JobsFilter!]!
  allNotifications: [Notification!]!
  allPricingPlans: [PricingPlan!]!
  allSubscriptions: [StripeSubscription!]!
  allTimeslots: [Timeslot!]!
  applicantDocumentByApplicantId(id: String): ApplicantDocument!
  applicantDocumentById(id: String!): ApplicantDocument!
  applicantNotifications(applicantId: String): [Notification!]!
  chatMessageById(id: String!): Message!
  chatRoomByCriteria(criteria: ChatRoomCriteriaInput!): ChatRoom!
  chatRoomById(id: String!): ChatRoom!
  chatRoomByMatch(jobActionId: String!): ChatRoom!
  companyById(id: String): Company!
  companyFairJob(id: String!): CompanyFairJob!
  companyUserNotifications(companyUserId: String!): [Notification!]!
  contactPersonByEmail(email: String!): ContactPerson!
  contactPersonsByCompanyId(companyId: String!): [ContactPerson!]!
  contactPersonsByFairId(fairId: String!): [ContactPerson!]!
  deviceTokenById(id: String!): DeviceToken!
  fairStats: FairStats!
  findAllAppointments: [Appointment!]!
  findAppointment(id: String!): Appointment!
  findAppointmentWithFilters(filter: AppointmentFilterInput, skip: Float, take: Float): AppointmentPaginated!
  findAppointmentsByApplicant(applicantId: String): [Appointment!]!
  findAppointmentsByCompany(companyId: String!): [Appointment!]!
  findAppointmentsByContactPerson(contactPersonId: String!): [Appointment!]!
  findAppointmentsByFair(fairId: String!): [Appointment!]!
  findAvailableSlots(fairId: String!): [ContactPersonTimeslot!]!
  findByContactPersonId(contactPersonId: String!): [ContactPersonTimeslot!]!
  findByFair(fairId: String!): [ContactPersonTimeslot!]!
  findCompanyFairContactPersonsByContactPersonId(contactPersonId: String!): [CompanyFairContactPerson!]!
  findFairByCompanyId(companyId: String!): [CompanyFairParticipation!]!
  findFairContactPersonByCompany(companyId: String!): [CompanyFairContactPerson!]!
  findFairContactPersonByFair(fairId: String!): [CompanyFairContactPerson!]!
  findFairContactPersonByParticipation(participationId: String!): [CompanyFairContactPerson!]!
  findFairsByCompanyId(companyId: String!): [Fair!]!
  findJobAdsByCompanyIdForApplicant(companyId: String!): [JobAdvert!]!
  findJobAdsByCompanyIdForCompany(companyId: String!): [JobAdvert!]!
  findNotification(id: String!): Notification!
  findParticipationByFairAndCompany(companyId: String!, fairId: String!): CompanyFairParticipation!
  findParticipationByFairId(fairId: String!): [CompanyFairParticipation!]!
  findTimeslotByDateRange(endDate: DateTime!, startDate: DateTime!): [Timeslot!]!
  findTimeslotByFairId(fairId: String!): [Timeslot!]!
  findUsersByCompany(companyId: String): [CompanyUser!]!
  generateAgoraToken(createAgoraInput: CreateAgoraInput!): Agora!
  getAllCompanyFairContactPersons: [CompanyFairContactPerson!]!
  getApplicant(applicantId: String): Applicant!
  getCompanyFairContactPersonById(id: String!): CompanyFairContactPerson!
  getCompanyFairJobByFairJobId(fairJobId: String!): [CompanyFairJob!]!
  getCompanyFairJobByParticipationId(companyParticipationId: String!): [CompanyFairJob!]!
  getCompanyFairParticipation(id: String!): CompanyFairParticipation!
  getCompanyUser(companyUserId: String!): CompanyUser!
  getContactPerson(id: String!): ContactPerson!
  getContactPersonTimeslot(id: String!): ContactPersonTimeslot!
  getFair(id: String!): Fair!
  getFairDay(id: String!): FairDay!
  getFairDayByDate(date: DateTime!): [FairDay!]!
  getFairDayByFair(fairId: String!): [FairDay!]!
  getFairJob(id: String!): FairJob!
  getFairJobByCompanyParticipationId(companyParticipationId: String!): [FairJob!]!
  getFairJobByFairId(fairId: String!): [FairJob!]!
  getFairJobByTitle(title: String!): FairJob!
  getFgadmin(id: String!): Fgadmin!
  getFilterOptions: FilterOptionsResponse!
  getJobsFilterByApplicantId(applicantId: String): JobsFilter!
  getSubscriptionById(id: String!): StripeSubscription!
  getTimeslot(id: String!): Timeslot!
  jobActionById(id: String!): JobAction!
  jobActionHistory: [JobActionHistory!]!
  jobActionsByApplicantId(applicantId: String): [JobAction!]!
  jobAdById(id: String!): JobAdvert!
  jobAdByIdForCompany(id: String!): JobAdvert
  jobAdStatsByCompanyId(id: String!): JobAdvertStats!
  jobAdStatsById(id: String!): JobAdvertStats!
  jobAdsBookmarked(applicantId: String!): [JobAdvert!]!
  jobAdsByCompanyId(companyId: String!): [JobAdvert!]!
  jobAdsByFilter(applicantId: String, filterOptions: JobAdvertFilterOptionsInput, includeDisliked: Boolean): [JobAdvert!]!
  jobAdsByLocation(latitude: Float, longitude: Float, radius: Float): [JobAdvert!]!
  jobAdsNotBookmarked(applicantId: String!): [JobAdvert!]!
  jobAdsSeen(applicantId: String!): [JobAdvert!]!
  jobAdsUnSeen(applicantId: String!): [JobAdvert!]!
  jobCategoryById(id: String!): JobCategory!
  jobLikesByAdvertId(advertId: String!): [JobAction!]!
  jobsFilterById(id: String!): JobsFilter!
  paginatedApplicants(paginationInput: PaginationInput): PaginatedApplicantsResponse!
  paginatedCompanies(paginationInput: PaginationInput): PaginatedCompaniesResponse!
  paginatedJobAds(paginationInput: PaginationInput): PaginatedJobAdvertsResponse!
  partnerLinks: [PartnerLink!]!
  pricingPlan(id: ID!): PricingPlan!
  retrievePaymentMethods(customerInput: CustomerIdInput!): PaymentMethodResponseDto!
  retrieveStripeCustomer(customerInput: CustomerIdInput!): CreateStripeCustomerResponseDto!
  retrieveSubscriptionMetadata(subscriptionIdInput: SubscriptionIdInput!): SubscriptionMetadataResponseDto!
  sendNewLikeEmail(data: NewLikeInput!): String!
  sendRestPasswordEmail(data: ResetPasswordInput!): String!
  sendWelcomeEmail(data: WelcomeEmailInput!): String!
  subscriptionsByCompanyId(companyId: String!): [StripeSubscription!]!
}

input RegisterDto {
  firebaseUid: String
}

type RegisterResponse {
  error: ErrorType
  user: User
}

input ResetPasswordInput {
  email: String!
  resetLink: String!
}

type Sepa {
  bank_code: String
  branch_code: String
  country: String
  fingerprint: String
  last4: String
}

type StaleImage {
  id: String!
  url: String!
}

input StripeAddressInput {
  """City"""
  city: String!

  """Country"""
  country: String!

  """Address line 1"""
  line1: String!

  """Address line 2"""
  line2: String

  """Postal code"""
  postal_code: String!

  """State"""
  state: String
}

type StripeCouponResponseDto {
  amount_off: Float
  created: Float!
  currency: String
  duration: String!
  duration_in_months: Float
  id: String!
  livemode: Boolean!
  max_redemptions: Float
  name: String
  object: String!
  percent_off: Float!
  redeem_by: Float
  times_redeemed: Float!
  valid: Boolean!
}

input StripeCustomerInput {
  """Address"""
  address: StripeAddressInput
  description: String

  """email"""
  email: String!

  """name"""
  name: String!
  phone: String

  """Shipping"""
  shipping: StripeShippingInput
}

type StripeInvoiceResponseDto {
  account_country: String!
  account_name: String!
  amount_due: Float!
  amount_paid: Float!
  amount_remaining: Float!
  amount_shipping: Float!
  application: Float
  application_fee_amount: Float
  attempt_count: Float!
  attempted: Boolean!
  auto_advance: Boolean!
  billing_reason: String!
  charge: Float
  collection_method: String!
  created: Float!
  currency: String!
  customer: String!
  customer_address: String
  customer_email: String!
  customer_name: String!
  customer_phone: String
  customer_tax_exempt: String!
  customer_tax_ids: [String!]!
  default_payment_method: String
  default_tax_rates: [String!]!
  description: String
  discount: String
  discounts: [String!]!
  due_date: String
  ending_balance: String
  footer: String
  from_invoice: String
  hosted_invoice_url: String
  id: String!
  invoice_pdf: String
  issuer: Issuer!
  last_finalization_error: String
  latest_revision: String
  lines: Lines!
  livemode: Boolean!
  next_payment_attempt: String
  number: String
  object: String!
  on_behalf_of: String
  paid: Boolean!
  paid_out_of_band: Boolean!
  payment_intent: PaymentIntentResponseDto!
  payment_settings: String
  period_end: Float!
  period_start: Float!
  post_payment_credit_notes_amount: Float!
  pre_payment_credit_notes_amount: Float!
  quote: String
  receipt_number: String
  rendering_options: String
  shipping_cost: String
  shipping_details: String
  starting_balance: Float!
  statement_descriptor: String
  status: String!
  subscription: String
  subtotal: Float!
  subtotal_excluding_tax: Float!
  tax: String
  test_clock: String
  total: Float!
  total_discount_amounts: [String!]!
  total_excluding_tax: Float!
  total_tax_amounts: [String!]!
  transfer_data: String
  webhooks_delivered_at: Float!
}

type StripePromoCodeResponseDto {
  coupon: StripeCouponResponseDto!
  created: Float!
  id: String!
  livemode: Boolean!
  max_redemptions: Float
  object: String!
  times_redeemed: Float!
}

input StripeShippingInput {
  """Address"""
  address: StripeAddressInput!

  """Name"""
  name: String

  """Phone"""
  phone: String
}

type StripeSubscription {
  amountTotal: Float
  cancelAtPeriodEnd: Boolean
  checkoutSessionId: String!
  company: Company!
  companyId: String!
  currency: String!
  expiresAt: DateTime!
  id: String!
  invoiceId: String!
  isActive: Boolean
  jobAdvert: JobAdvert!
  jobAdvertId: String!
  paymentStatus: String!
  percent_off: Float!
  plan: String!
  status: String!
  stripeCustomerId: String
  stripeSubscriptionId: String!
  subscriptionId: String
}

input SubscriptionIdInput {
  """Stripe Subscription ID"""
  subscriptionId: String!
}

type SubscriptionMetadataResponseDto {
  advertId: String
  advertIds: String
  advertTitle: String
  advertTitles: String
  companyId: Float
}

type SuperUser {
  avatarImageUrl: String
  createdAt: DateTime!
  email: String
  id: String!
  name: String
  updatedAt: DateTime!
  user: User!
  userId: String!
}

input TimeRangeInput {
  endTime: String
  startTime: String
}

type Timeslot {
  endTime: DateTime!
  fair: Fair!
  fairId: String!
  id: String!
  startTime: DateTime!
}

input UpdateApplicantDocumentInput {
  documentPreviewUrl: String
  id: String!
  name: String
  storage: String
  url: String
}

input UpdateApplicantInput {
  availableFrom: DateTime
  birthDate: DateTime
  city: String
  description: String
  environment: Int
  firstName: String
  graduation: String
  lastActive: String
  lastName: String
  personality: Int
  profileImageUrl: String
  receiveNotifications: Boolean
  schoolName: String
  strengths: [String!]
  subjects: [String!]
  weaknesses: [String!]
}

input UpdateAppointmentInput {
  applicantId: String
  companyIsNew: Boolean
  contactPersonTimeslotId: String
  id: String!
  rejectReason: String
  status: AppointmentStatus
}

input UpdateChatRoomInput {
  id: String!
  status: String
}

input UpdateCompanyFairContactPersonInput {
  companyFairParticipationId: String
  contactPersonId: String
  id: String!
}

input UpdateCompanyFairJobInput {
  companyFairParticipationId: String
  description: String
  fairJobId: String
  id: String!
}

input UpdateCompanyFairParticipationInput {
  categoryIds: [String!]
  companyId: String
  fairId: String
  id: String!
  partnerLinkIds: [String!]
}

input UpdateCompanyInput {
  address: String
  city: String
  companyUserId: String
  country: String
  detailContent: String
  dynamicLink: String
  foundingYear: Float
  headerImageUrl: String
  isFairManaged: Boolean
  latitude: Float
  logoImageUrl: String
  longitude: Float
  name: String
  stripeCustomerId: String
  totalEmployees: Float
}

input UpdateCompanyUserInput {
  activeCompanyId: String
  avatarImageUrl: String
  email: String
  id: String!
  name: String
}

input UpdateContactPersonInput {
  companyId: String
  email: String
  id: String!
  name: String
  phone: String
  position: String
}

input UpdateContactPersonTimeslotInput {
  available: Boolean
  companyFairContactPersonId: String
  endTime: DateTime
  id: String!
  startTime: DateTime
}

input UpdateDeviceTokenInput {
  token: String
}

input UpdateFairDayInput {
  day: DateTime
  endTime: DateTime
  fairId: String
  id: String!
  startTime: DateTime
}

input UpdateFairInput {
  city: String
  contactPersonEmail: String
  contactPersonName: String
  description: String
  endDate: DateTime
  id: String!
  location: String
  locationName: String
  logoImageUrl: String
  name: String
  publisherLogoImageUrl: String
  publisherName: String
  registrationEndDate: DateTime
  registrationStartDate: DateTime
  startDate: DateTime
  status: String
}

input UpdateFairJobInput {
  id: String!
  title: String
}

input UpdateFgadminInput {
  avatarImageUrl: String
  email: String
  id: String!
  name: String
  type: String
}

input UpdateJobActionInput {
  applicantIsNew: Boolean
  companyIsNew: Boolean
  declineReason: String
  deletedFromApplicant: Boolean
  deletedFromCompany: Boolean
  id: String!
  state: ActionState
  status: String
}

input UpdateJobAdvertInput {
  activeFromDate: DateTime
  address: String
  approved: Boolean
  city: String
  companyName: String
  companyUserId: String
  declineReason: String
  description: String
  detailDescription: String
  district: String
  educationDuration: Float
  endDate: DateTime
  gehalt: [Float!]
  headerImageUrl: String
  holidayDays: Float
  imageUrl: String
  impressions: Float
  isDeclined: Boolean
  isDeleted: Boolean
  isDraft: Boolean
  jobCategoryId: String
  latitude: Float
  longitude: Float
  startDate: DateTime
  title: String
  type: String
  workHours: Float
}

input UpdateJobCategoryInput {
  id: String!
  imageUrl: String
  name: String
}

input UpdateJobsFilterInput {
  categories: [String!]
  currentLocation: String
  id: String!
  latitude: Float
  longitude: Float
  radius: Float
  type: [JobAdvertType!]
}

input UpdateMessageInput {
  authorName: String
  content: String
  deletedAt: DateTime
  deletedBy: String
  deletedById: String
  id: String!
  isApplicant: Boolean = false
  isCompany: Boolean
  isDeleted: Boolean
  isDelivered: Boolean
  isSeen: Boolean
  isSent: Boolean
}

input UpdatePartnerLinkInput {
  id: String!
  name: String
  url: String
}

input UpdatePricingPlanInput {
  billingPeriod: BillingPeriod
  currency: String
  description: String
  displayName: String
  displayOrder: Int
  durationDays: Int
  hasCustomBranding: Boolean
  id: String!
  isActive: Boolean
  isCustom: Boolean
  isPopular: Boolean
  name: String
  planType: PricingPlanType
  price: Float
  unlimitedJobAdverts: Boolean
}

input UpdateStripeCustomerInput {
  """Address"""
  address: StripeAddressInput
  description: String

  """email"""
  email: String

  """name"""
  name: String
  phone: String

  """Shipping"""
  shipping: StripeShippingInput
}

input UpdateSubscriptionInput {
  amountTotal: Float!
  checkoutSessionId: String!
  currency: String!
  expiresAt: DateTime!

  """Subscription ID"""
  id: String!
  invoiceId: String!
  isActive: Boolean! = false
  paymentStatus: String!
  percent_off: Float
  plan: String!
  status: String
  stripeCustomerId: String!
  stripeSubscriptionId: String!
}

input UpdateTimeslotInput {
  endTime: DateTime
  fairId: String
  id: String!
  startTime: DateTime
}

input UpdateUserRightInput {
  companyAdmin: Boolean
  createJobAd: Boolean
  createUser: Boolean
  deleteJobAd: Boolean
  editCompany: Boolean
  editJobAd: Boolean
  id: String!
  superAdmin: Boolean
  updateJobAd: Boolean
  viewApplicants: Boolean
  viewJobAd: Boolean
}

type User {
  firebaseUid: String!
  id: String!
}

type UserRight {
  company: Company!
  companyAdmin: Boolean
  companyId: String!
  companyUser: CompanyUser!
  createJobAd: Boolean
  createUser: Boolean
  deleteJobAd: Boolean
  editCompany: Boolean
  editJobAd: Boolean
  id: String!
  superAdmin: Boolean
  updateJobAd: Boolean
  viewApplicants: Boolean
  viewJobAd: Boolean
}

input WelcomeEmailInput {
  email: String!
  name: String!
  verificationLink: String
}