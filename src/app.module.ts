import {
    Module,
    NestModule,
    MiddlewareConsumer,
    RequestMethod,
} from '@nestjs/common'
import { JsonBodyMiddleware } from '@golevelup/nestjs-webhooks'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { GraphQLModule } from '@nestjs/graphql'
import { ApolloDriver } from '@nestjs/apollo'
import { join } from 'path'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { AuthModule } from './auth/auth.module'
import { SubscriptionModule } from './subscription/subscription.module'
import configs from './config'
import { RawBodyMiddleware } from './middlewares/raw-body.middleware'
import { JobsFilterModule } from './jobs-filter/jobs-filter.module'
import { UserRightModule } from './user-right/user-right.module'
import { AgoraModule } from './agora/agora.module'
import { MailerModule } from '@nestjs-modules/mailer'
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter'
import { StripeModule } from '@golevelup/nestjs-stripe'
import { Context } from 'graphql-ws'
import { admin } from './auth/firebase-admin.module'
import { ScheduleModule } from '@nestjs/schedule'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { CompanyUserModule } from './company-user/company-user.module'
import { JobAdvertModule } from './job-advert/job-advert.module'
import { CompanyModule } from './company/company.module'
import { UserModule } from './user/user.module'
import { ApplicantModule } from './applicant/applicant.module'
import { JobCategoryModule } from './job-category/job-category.module'
import { ApplicantDocumentModule } from './applicant-document/applicant-document.module'
import { JobActionsModule } from './job-actions/job-actions.module'
import { DeviceTokenModule } from './device-token/device-token.module'
import { ChatRoomModule } from './chat-room/chat-room.module'
import { MessageModule } from './message/message.module'
import { EmailServerModule } from './email-server/email-server.module'
import { DynamicLinkModule } from './dynamic-link/dynamic-link.module'
import { NotificationModule } from './notification/notification.module'
import { SuperUserModule } from './super-user/super-user.module'
import { JobActionHistoryModule } from './job-action-history/job-action-history.module'
import { StaleImagesModule } from './stale-images/stale-images.module'
import { FirebaseActionsModule } from './firebase-actions/firebase-actions.module'
import { PubSubModule } from './pub-sub/pub-sub.module'
import { MinVersionModule } from './min-version/min-version.module'
import { StatisticsModule } from './statistics/statistics.module'
import { BackendMigrationModule } from './backend-migration/backend-migration.module'
import { FairModule } from './fair/fair.module'
import { CompanyFairJobModule } from './company-fair-job/company-fair-job.module'
import { CompanyFairParticipationModule } from './company-fair-participation/company-fair-participation.module'
import { CompanyFairContactPersonModule } from './company-fair-contact-person/company-fair-contact-person.module'
import { FairJobModule } from './fair-job/fair-job.module'
import { ContactPersonModule } from './contact-person/contact-person.module'
import { TimeslotModule } from './timeslot/timeslot.module'
import { AppointmentModule } from './appointment/appointment.module'
import { ContactPersonTimeslotModule } from './contact-person-timeslot/contact-person-timeslot.module'
import { FgadminModule } from './fgadmin/fgadmin.module'
import { JobActionPublisherService } from './job-actions/job-action-publish/job-action-publisher.service'
import { PusherPubSubModule } from './pub-sub/pusher-pub-sub.module'
import { FairDayModule } from './fair-day/fair-day.module'
import { PartnerLinkModule } from './partner-link/partner-link.module'
import { PricingPlanModule } from './pricing-plan/pricing-plan.module'

@Module({
    imports: [
        PubSubModule,
        GraphQLModule.forRootAsync({
            imports: [ConfigModule, AppModule, PubSubModule],
            inject: [ConfigService],
            driver: ApolloDriver,
            useFactory: async (configService: ConfigService) => {
                return {
                    installSubscriptionHandlers: true,
                    playground: configService.get('NODE_ENV') !== 'production',
                    autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
                    sortSchema: true,
                    introspection: true,
                    cors: {
                        origin: '*',
                        credentials: true,
                        allowedHeaders: [
                            'Accept',
                            'Authorization',
                            'Content-Type',
                            'X-Requested-With',
                            'apollo-require-preflight',
                            'x-apollo-operation-name',
                        ],
                        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
                    },
                    subscriptions: {
                        'graphql-ws': {
                            onConnect: async (context: Context<any, any>) => {
                                const { connectionParams } = context
                                let tokenObj: Readonly<any>
                                if (typeof connectionParams === 'string') {
                                    tokenObj = JSON.parse(connectionParams)
                                } else {
                                    tokenObj = connectionParams
                                }
                                const user = await admin
                                    .auth()
                                    .verifyIdToken(tokenObj.token)

                                context.extra.user = user

                                return true
                            },
                        },
                        'subscriptions-transport-ws': {
                            keepalive: 20000,
                            onConnect: async (connectionParams: any) => {
                                let tokenObj = null

                                if (typeof connectionParams === 'string') {
                                    tokenObj = JSON.parse(connectionParams)
                                } else {
                                    tokenObj = connectionParams
                                }

                                if (!tokenObj?.token) {
                                    throw new Error('Token not provided')
                                }

                                const user = await admin
                                    .auth()
                                    .verifyIdToken(tokenObj.token)

                                if (!user) {
                                    throw new Error('Invalid token')
                                }
                                return { user }
                            },
                        },
                    },
                    context: ({ req, res, connection }) => {
                        if (connection) {
                            return {
                                req,
                                res,
                                user: connection.context.extra.user,
                            }
                        }
                        return { req, res }
                    },
                }
            },
        }),
        MailerModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    transport: {
                        host: configService.get('MAIL_HOST'),
                        port: configService.get('MAIL_PORT'),
                        secure: true, //TODO: Change to true if using SSL
                        auth: {
                            user: configService.get('MAIL_USER'),
                            pass: configService.get('MAIL_PASS'),
                        },
                    },
                    defaults: {
                        from: `"No Reply"`,
                    },
                    template: {
                        dir: join(__dirname, '../templates'),
                        adapter: new HandlebarsAdapter(),
                        options: {
                            strict: true,
                        },
                    },
                }
            },
        }),
        ConfigModule.forRoot({ cache: true, isGlobal: true, load: [configs] }),
        StripeModule.forRoot(StripeModule, {
            apiKey: configs().STRIPE_CONFIG.apiKey,
            webhookConfig: {
                stripeSecrets: {
                    account:
                        configs().STRIPE_CONFIG.webhookConfig.stripeSecrets
                            .account,
                },
            },
        }),
        ScheduleModule.forRoot(),
        StatisticsModule,
        UserModule,
        MinVersionModule,
        AuthModule,
        CompanyModule,
        JobAdvertModule,
        CompanyUserModule,
        ApplicantModule,
        JobCategoryModule,
        SubscriptionModule,
        ApplicantDocumentModule,
        JobActionsModule,
        JobsFilterModule,
        DeviceTokenModule,
        ChatRoomModule,
        MessageModule,
        UserRightModule,
        AgoraModule,
        EmailServerModule,
        DynamicLinkModule,
        NotificationModule,
        EventEmitterModule.forRoot(),
        SuperUserModule,
        JobActionHistoryModule,
        StaleImagesModule,
        FirebaseActionsModule,
        BackendMigrationModule,
        FairModule,
        CompanyFairJobModule,
        CompanyFairParticipationModule,
        CompanyFairContactPersonModule,
        FairJobModule,
        ContactPersonModule,
        TimeslotModule,
        AppointmentModule,
        PusherPubSubModule,
        ContactPersonTimeslotModule,
        FgadminModule,
        FairDayModule,
        PartnerLinkModule,
        PricingPlanModule,
    ],
    controllers: [AppController],
    providers: [AppService, JobActionPublisherService],
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(RawBodyMiddleware)
            .forRoutes({
                path: '/subscription/handle-webhook',
                method: RequestMethod.POST,
            })
            .apply(JsonBodyMiddleware)
            .forRoutes('*')
    }
}
