import { Resolver, Mutation, Args, Query, Context } from '@nestjs/graphql'
import { SubscriptionService } from './subscription.service'
import { Request } from 'express'
import { StripeSubscription } from './entities/subscription.entity'
import { CreateSessionResponseDto } from './dto/create-checkout-session.response'
import { CreateCheckoutSessionInput } from './dto/create-checkout-session.input'
import { CreatePortalSessionInput } from './dto/create-portal-session.input'
import {
    CreateStripeSubscriptionInput,
    CreateSubscriptionInput,
} from './dto/create-subscription.input'
import { UpdateSubscriptionInput } from './dto/update-subscription.input'
import {
    ConfirmPaymentIntentInput,
    CreatePaymentIntentInput,
    CustomerIdInput,
    InvoiceIdInput,
    StripeCustomerInput,
    SubscriptionIdInput,
    UpdateStripeCustomerInput,
} from './dto/stripe-subscription-management.input'
import {
    CancelSubscriptionResponseDto,
    CreateStripeCustomerResponseDto,
    CreateStripeSubscriptionResponseDto,
    SubscriptionMetadataResponseDto,
} from './dto/stripe-subscription-management.response'
import { StripeInvoiceResponseDto } from './dto/stripe-invoice.reponse'
import { PaymentIntentResponseDto } from './dto/payment-intent.response'
import { PaymentMethodResponseDto } from './dto/payment-method.response'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'
import {
    StripeCouponResponseDto,
    StripePromoCodeResponseDto,
} from './dto/stripe-coupon.response'

@UseGuards(CompanyGuard)
@Resolver(() => StripeSubscription)
export class SubscriptionResolver {
    constructor(private readonly subscriptionService: SubscriptionService) {}
    @Mutation(() => CreateSessionResponseDto)
    createCheckoutSession(
        @Context() context: any,
        @Args('customer', { type: () => String, nullable: true })
        customer: string,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string,
        @Args('createCheckoutSessionInput')
        createCheckoutSessionInput: CreateCheckoutSessionInput
    ) {
        const bCompanyId = context?.req?.user?.companyId || companyId
        return this.subscriptionService.createCheckoutSession(
            customer,
            bCompanyId,
            createCheckoutSessionInput
        )
    }
    @Mutation(() => CreateSessionResponseDto)
    createSetupCheckoutDetails(
        @Context() context: any,
        @Args('customer', { type: () => String, nullable: true })
        customer: string,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string,
        @Args('type', {
            type: () => String,
            nullable: true,
            defaultValue: 'checkout',
        })
        type: string
    ) {
        const bCompanyId = context?.req?.user?.companyId || companyId
        return this.subscriptionService.createSetupCheckout(
            customer,
            bCompanyId,
            type
        )
    }

    @Mutation(() => CreateSessionResponseDto)
    createBulkCheckoutSession(
        @Context() context: any,
        @Args('customer', { type: () => String, nullable: true })
        customer: string,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string,
        @Args('createCheckoutSessionInput', {
            type: () => [CreateCheckoutSessionInput],
        })
        createCheckoutSessionInput: CreateCheckoutSessionInput[]
    ) {
        const bCompanyId = context?.req?.user?.companyId || companyId
        return this.subscriptionService.createBulkCheckoutSession(
            customer,
            bCompanyId,
            createCheckoutSessionInput
        )
    }
    @Mutation(() => CreateSessionResponseDto)
    createPortalSession(
        @Args('createCheckoutSessionInput')
        createPortalSessionInput: CreatePortalSessionInput
    ) {
        return this.subscriptionService.createPortalSession(
            createPortalSessionInput
        )
    }

    @Mutation(() => CancelSubscriptionResponseDto)
    cancelSubscription(
        @Args('subscriptionIdInput')
        subscriptionIdInput: SubscriptionIdInput
    ) {
        return this.subscriptionService.cancelStripeSubscription(
            subscriptionIdInput
        )
    }

    @Mutation(() => CancelSubscriptionResponseDto)
    resumeSubscription(
        @Args('subscriptionIdInput')
        subscriptionIdInput: SubscriptionIdInput
    ) {
        return this.subscriptionService.resumeStripeSubscription(
            subscriptionIdInput
        )
    }

    @Mutation(() => StripeInvoiceResponseDto)
    retrieveSubscriptionInvoice(
        @Args('invoiceIdInput')
        invoiceIdInput: InvoiceIdInput
    ) {
        return this.subscriptionService.retrieveStripeInvoice(invoiceIdInput)
    }
    @Mutation(() => StripeInvoiceResponseDto)
    sendSubscriptionInvoice(
        @Args('invoiceIdInput')
        invoiceIdInput: InvoiceIdInput
    ) {
        return this.subscriptionService.sendStripeInvoice(invoiceIdInput)
    }

    @Query(() => SubscriptionMetadataResponseDto)
    retrieveSubscriptionMetadata(
        @Args('subscriptionIdInput')
        subscriptionIdInput: SubscriptionIdInput
    ) {
        return this.subscriptionService.retrieveSubscriptionMetadata(
            subscriptionIdInput
        )
    }

    @Query(() => CreateStripeCustomerResponseDto)
    retrieveStripeCustomer(
        @Args('customerInput')
        customerIdInput: CustomerIdInput
    ) {
        return this.subscriptionService.getStripeCustomer(customerIdInput)
    }

    @Mutation(() => StripeCouponResponseDto)
    retrieveStripeCoupon(
        @Args('promoCode', { type: () => String }) promoCode: string
    ) {
        return this.subscriptionService.getStripeCouponByPromoCode(promoCode)
    }

    @Mutation(() => StripePromoCodeResponseDto)
    retrieveStripePromoCode(
        @Args('promoCode', { type: () => String }) promoCode: string
    ) {
        return this.subscriptionService.getStripePromoCodeDetails(promoCode)
    }

    @Query(() => PaymentMethodResponseDto)
    retrievePaymentMethods(
        @Args('customerInput')
        customerIdInput: CustomerIdInput
    ) {
        return this.subscriptionService.retrievePaymentMethodList(
            customerIdInput
        )
    }

    @Mutation(() => CreateStripeSubscriptionResponseDto)
    async createStripeSubscription(
        @Args('createStripeSubscriptionInput')
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        @Args('subType', { type: () => String, nullable: true }) subType: string
    ) {
        return this.subscriptionService.createStripeSubscription(
            createStripeSubscriptionInput,
            subType
        )
    }
    @Mutation(() => PaymentIntentResponseDto)
    createPaymentIntent(
        @Args('createPaymentIntentInput')
        createPaymentIntentInput: CreatePaymentIntentInput
    ) {
        return this.subscriptionService.createPaymentIntent(
            createPaymentIntentInput
        )
    }

    @Mutation(() => PaymentIntentResponseDto)
    confirmPaymentIntent(
        @Args('confirmPaymentIntentInput')
        confirmPaymentIntentInput: ConfirmPaymentIntentInput,
        @Context() context: { req: Request }
    ) {
        const customerIpAddress = context.req.ip
        const customerUserAgent = context.req.headers['user-agent']

        return this.subscriptionService.confirmPayment(
            confirmPaymentIntentInput,
            customerIpAddress,
            customerUserAgent
        )
    }

    @Mutation(() => CreateStripeSubscriptionResponseDto)
    async generateSubscriptionInvoice(
        @Args('createStripeSubscriptionInput')
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        @Args('customerDetails', { nullable: true })
        customerDetails: UpdateStripeCustomerInput,
        @Context() context: any,
        @Args('companyId', { nullable: true })
        companyId: String
    ) {
        const bCompanyId =
            context?.req?.user?.companyId ||
            companyId ||
            createStripeSubscriptionInput.companyId
        if (
            createStripeSubscriptionInput.jobAdverts &&
            createStripeSubscriptionInput.jobAdverts.length > 1
        ) {
            //Bulk version here::
            return this.subscriptionService.generateBulkSubscriptionInvoices(
                bCompanyId,
                createStripeSubscriptionInput,
                customerDetails
            )
        } else {
            return this.subscriptionService.generateSubscriptionInvoice(
                bCompanyId,
                createStripeSubscriptionInput,
                customerDetails
            )
        }
    }

    @Mutation(() => PaymentIntentResponseDto)
    async subscribeToPremium(
        @Args('createStripeSubscriptionInput')
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        @Args('paymentMethodId', { type: () => String, nullable: true })
        paymentMethodId: string,
        @Context() context: { req: Request }
    ) {
        const customerIpAddress = context.req.ip
        const customerUserAgent = context.req.headers['user-agent']
        if (
            createStripeSubscriptionInput.jobAdverts &&
            createStripeSubscriptionInput.jobAdverts.length > 1
        ) {
            return this.subscriptionService.handleBulkCustomerCheckout(
                createStripeSubscriptionInput,
                paymentMethodId,
                customerIpAddress,
                customerUserAgent
            )
        } else {
            return this.subscriptionService.handleCustomerCheckout(
                createStripeSubscriptionInput,
                paymentMethodId,
                customerIpAddress,
                customerUserAgent
            )
        }
    }
    @Mutation(() => CreateStripeCustomerResponseDto)
    async createStripeCustomer(
        @Context() context: any,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string,
        @Args('createStripeCustomerInput')
        stripeCustomerInput: StripeCustomerInput
    ) {
        const bCompanyId = companyId || context?.req?.user?.companyId
        return this.subscriptionService.createStripeCustomer(
            bCompanyId,
            stripeCustomerInput
        )
    }

    @Mutation(() => CreateStripeCustomerResponseDto)
    async updateStripeCustomer(
        @Args('customerId', { type: () => String, nullable: true })
        customerId: string,
        @Args('createStripeCustomerInput')
        stripeCustomerInput: StripeCustomerInput
    ) {
        return this.subscriptionService.updateStripeCustomer(
            customerId,
            stripeCustomerInput
        )
    }

    @Mutation(() => StripeSubscription)
    createSubscription(
        @Args('jobAdvertId', { type: () => String }) jobAdvertId: string,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string,
        @Args('createSubscriptionInput')
        createSubscriptionInput: CreateSubscriptionInput
    ) {
        return this.subscriptionService.create(
            createSubscriptionInput,
            jobAdvertId,
            companyId
        )
    }

    @Query(() => [StripeSubscription], { name: 'allSubscriptions' })
    findAll() {
        return this.subscriptionService.findAll()
    }

    @Query(() => [StripeSubscription], { name: 'subscriptionsByCompanyId' })
    findByCompany(
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.subscriptionService.findByCompany(companyId)
    }

    @Query(() => StripeSubscription, { name: 'getSubscriptionById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.subscriptionService.findOne(id)
    }

    @Mutation(() => StripeSubscription)
    updateSubscription(
        @Args('updateSubscriptionInput')
        updateSubscriptionInput: UpdateSubscriptionInput
    ) {
        return this.subscriptionService.update(updateSubscriptionInput)
    }
}
