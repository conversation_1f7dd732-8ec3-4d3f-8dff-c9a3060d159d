import { ObjectType, Field } from '@nestjs/graphql'
import { JobAdvert } from '../../job-advert/entities/job-advert.entity'
import { PlanType } from '@prisma/client'
import { Company } from '../../company/entities/company.entity'

@ObjectType()
export class StripeSubscription {
    @Field()
    id: string

    @Field()
    plan: PlanType

    @Field({ nullable: true })
    stripeCustomerId?: string

    @Field({ nullable: true })
    subscriptionId?: string

    @Field({ nullable: true })
    isActive?: boolean

    @Field({ nullable: true, defaultValue: false })
    cancelAtPeriodEnd?: boolean

    @Field()
    jobAdvertId: string

    @Field(() => JobAdvert)
    jobAdvert: JobAdvert

    @Field()
    companyId: string

    @Field(() => Company)
    company: Company

    @Field(() => Date)
    expiresAt: Date

    @Field()
    checkoutSessionId: string

    @Field({ nullable: true, defaultValue: 0 })
    amountTotal: number

    @Field()
    paymentStatus: string

    @Field()
    percent_off: number

    @Field()
    invoiceId: string

    @Field()
    currency: string

    @Field()
    status: string

    @Field()
    stripeSubscriptionId: string
}
