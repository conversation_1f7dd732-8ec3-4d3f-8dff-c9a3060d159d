import { ObjectType, Field } from '@nestjs/graphql'
import { JobAdvert } from '../../job-advert/entities/job-advert.entity'
import { CompanyUser } from '../../company-user/entities/company-user.entity'

@ObjectType()
export class Company {
    @Field()
    id: string

    @Field(() => String)
    address: string

    @Field()
    city: string

    @Field()
    country: string

    @Field()
    name: string

    @Field({ nullable: true })
    latitude: number

    @Field({ nullable: true })
    longitude: number

    @Field({ nullable: true })
    detailContent: string

    @Field({ nullable: true })
    foundingYear: number

    @Field({ nullable: true })
    headerImageUrl: string

    @Field({ nullable: true })
    isFairManaged: boolean

    @Field({ nullable: true })
    stripeCustomerId: string

    @Field({ nullable: true })
    logoImageUrl: string

    @Field({ nullable: true })
    totalEmployees: number

    @Field({ nullable: true })
    dynamicLink: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date

    @Field({ nullable: true })
    companyUserId: string

    @Field({ nullable: true })
    isInFair: boolean

    @Field(() => [JobAdvert], { nullable: true })
    jobAdvert: JobAdvert[]

    @Field(() => [CompanyUser], { nullable: true })
    companyUsers: CompanyUser[]
}
