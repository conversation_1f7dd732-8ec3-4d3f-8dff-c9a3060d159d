import { InputType, Field, Float } from '@nestjs/graphql'
import {
    IsString,
    IsOptional,
    IsNumber,
    IsUrl,
    IsBoolean,
} from 'class-validator'

@InputType()
export class CreateCompanyInput {
    @Field()
    @IsString()
    address: string

    @Field()
    @IsString()
    city: string

    @Field()
    @IsString()
    country: string

    @Field()
    @IsString()
    name: string

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    latitude?: number

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    longitude?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    detailContent?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    stripeCustomerId?: string

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    foundingYear?: number

    @Field({ nullable: true })
    @IsUrl()
    @IsOptional()
    headerImageUrl?: string

    @Field({ nullable: true })
    @IsUrl()
    @IsOptional()
    logoImageUrl?: string

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    totalEmployees?: number

    @Field({ nullable: true })
    @IsUrl()
    @IsOptional()
    dynamicLink?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    companyUserId: string

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isFairManaged: boolean

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    hasUnlimitedSubscription?: boolean

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    unlimitedSubscriptionId?: string

    @Field({ nullable: true })
    @IsOptional()
    unlimitedSubscriptionExpiresAt?: Date
}
