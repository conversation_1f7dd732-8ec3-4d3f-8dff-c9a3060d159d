import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { CompanyService } from './company.service'
import { Company } from './entities/company.entity'
import { CreateCompanyInput } from './dto/create-company.input'
import { UpdateCompanyInput } from './dto/update-company.input'
import { Company as CompanyType } from '@prisma/client'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { SuperGuard } from '../guards/super-guard'
import { PaginationInput } from '../common/dto/pagination.input'
import { PaginatedCompaniesResponse } from './dto/paginated-companies.response'

@Resolver(() => Company)
export class CompanyResolver {
    constructor(private readonly companyService: CompanyService) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Company)
    createCompany(
        @Args('companyInput') createCompanyInput: CreateCompanyInput
    ) {
        return this.companyService.create(createCompanyInput)
    }

    @UseGuards(SuperGuard)
    @Mutation(() => Company)
    createCompanyByAdmin(
        @Args('companyInput') createCompanyInput: CreateCompanyInput
    ) {
        return this.companyService.adminCreate(createCompanyInput)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Company], { name: 'allCompanies' })
    findAll() {
        return this.companyService.findAll()
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => PaginatedCompaniesResponse, { name: 'paginatedCompanies' })
    findPaginated(
        @Args('paginationInput', { nullable: true })
        paginationInput?: PaginationInput
    ) {
        return this.companyService.findPaginated(
            paginationInput || { page: 1, limit: 10 }
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => Company, { name: 'companyById' })
    findOne(
        @Context() context: any,
        @Args('id', { type: () => String, nullable: true }) id: string
    ): Promise<CompanyType> {
        const bCompanyId = context?.req?.user?.companyId || id
        return this.companyService.findOne(bCompanyId)
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => Company)
    updateCompany(
        @Context() context: any,
        @Args('updateCompanyInput', {
            type: () => UpdateCompanyInput,
        })
        updateCompanyInput: UpdateCompanyInput,
        @Args('id', { type: () => String, nullable: true }) id: string
    ) {
        const bCompanyId = context?.req?.user?.companyId || id
        return this.companyService.update(bCompanyId, updateCompanyInput)
    }

    @Mutation(() => Company)
    manageFair(
        @Context() context: any,
        @Args('isFairManaged', { type: () => Boolean }) isFairManaged: boolean,
        @Args('id', { type: () => String, nullable: true }) id: string
    ) {
        const bCompanyId = context?.req?.user?.companyId || id
        return this.companyService.manageFair(bCompanyId, isFairManaged)
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => Company)
    removeCompany(@Args('id', { type: () => String }) id: string) {
        return this.companyService.remove(id)
    }

    // @UseGuards(CompanyGuard)
    @Query(() => [Company], { name: 'allCompaniesWithFairStatus' })
    findAllWithFairStatus(
        @Args('fairId', { type: () => String }) fairId: string
    ) {
        return this.companyService.findAllWithFairStatus(fairId)
    }
}
