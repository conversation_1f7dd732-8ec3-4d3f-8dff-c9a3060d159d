import { Injectable, NotFoundException } from '@nestjs/common'
import { Company } from '@prisma/client'
import { PrismaService } from '../prisma.service'
import { CreateCompanyInput } from './dto/create-company.input'
import { UpdateCompanyInput } from './dto/update-company.input'
import { EmailServerService } from '../email-server/email-server.service'
import { admin } from '../auth/firebase-admin.module'
import { AuthService } from '../auth/auth.service'
import { PaginationInput } from '../common/dto/pagination.input'
import { IPaginatedType } from '../common/dto/pagination-response'

@Injectable()
export class CompanyService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly authService: AuthService,
        private readonly emailService: EmailServerService
    ) {}

    async generateVerificationEmail(email: string) {
        return await admin.auth().generateEmailVerificationLink(email)
    }

    async create(createCompanyInput: CreateCompanyInput): Promise<Company> {
        const companyExists = await this.prisma.company.findFirst({
            where: {
                name: createCompanyInput.name,
                country: createCompanyInput.country,
                city: createCompanyInput.city,
            },
        })

        if (companyExists) {
            throw new NotFoundException('Company already exists')
        }

        const newCompany = await this.prisma.company.create({
            data: {
                ...createCompanyInput,
                companyUsers: {
                    connect: {
                        id: createCompanyInput.companyUserId,
                    },
                },
            },
        })

        const companyUser = await this.prisma.companyUser.findUnique({
            where: {
                id: createCompanyInput.companyUserId,
            },
        })

        if (!companyUser.activeCompanyId) {
            const userRights = { superAdmin: true }

            await this.prisma.companyUser.update({
                where: {
                    id: createCompanyInput.companyUserId,
                },
                data: {
                    activeCompanyId: newCompany?.id,
                    companies: {
                        connect: {
                            id: newCompany?.id,
                        },
                    },
                },
            })

            try {
                await this.prisma.userRights.create({
                    data: {
                        ...userRights,
                        company: {
                            connect: {
                                id: newCompany?.id,
                            },
                        },
                        companyUser: {
                            connect: {
                                id: companyUser.id,
                            },
                        },
                    },
                })
            } catch (error) {
                console.error('Error creating user rights:', error)
                throw error
            }
        }

        const verificationEmail = await this.authService.createEmailVerifyLink(
            companyUser.email
        )

        await this.emailService.sendWelcomeEmail({
            email: companyUser.email,
            name: companyUser.name,
            verificationLink: verificationEmail,
        })

        if (!newCompany) throw new NotFoundException('Company not created')

        return newCompany
    }

    async adminCreate(
        createCompanyInput: CreateCompanyInput
    ): Promise<Company> {
        const companyExists = await this.prisma.company.findFirst({
            where: {
                name: createCompanyInput.name,
                country: createCompanyInput.country,
                city: createCompanyInput.city,
            },
        })

        if (companyExists) {
            throw new NotFoundException('Company already exists')
        }

        const newCompany = await this.prisma.company.create({
            data: {
                ...createCompanyInput,
            },
        })

        if (!newCompany) throw new NotFoundException('Company not created')

        return newCompany
    }

    async findAll() {
        return this.prisma.company.findMany({
            include: {
                companyUsers: true,
            },
        })
    }

    //Find all companies but add a `isInFair` boolean prop to the ones that are in the fair
    async findAllWithFairStatus(fairId: string) {
        if (!fairId) throw new NotFoundException('FairId not provided')

        const companies = await this.prisma.company.findMany({
            include: {
                companyUsers: true,
            },
        })

        const companiesInFair =
            await this.prisma.companyFairParticipation.findMany({
                where: {
                    fairId: fairId,
                },
                select: {
                    companyId: true,
                },
            })

        const companiesWithFairStatus = companies
            .map((company) => {
                const isInFair = companiesInFair.some(
                    (companyInFair) => companyInFair.companyId === company.id
                )
                return {
                    ...company,
                    isInFair: isInFair,
                }
            })
            .sort((a, b) => {
                if (a.isInFair && !b.isInFair) return 1
                if (!a.isInFair && b.isInFair) return -1
                return 0
            })

        return companiesWithFairStatus
    }

    async findPaginated(
        paginationInput: PaginationInput
    ): Promise<IPaginatedType<Company>> {
        const { page = 1, limit = 10, search = '' } = paginationInput
        const skip = (page - 1) * limit

        const where = search
            ? {
                  OR: [
                      {
                          name: {
                              contains: search,
                              mode: 'insensitive' as const,
                          },
                      },
                      {
                          city: {
                              contains: search,
                              mode: 'insensitive' as const,
                          },
                      },
                      {
                          address: {
                              contains: search,
                              mode: 'insensitive' as const,
                          },
                      },
                  ],
              }
            : {}

        const [items, totalItems] = await Promise.all([
            this.prisma.company.findMany({
                where,
                include: { companyUsers: true },
                skip,
                take: limit,
            }),
            this.prisma.company.count({
                where,
            }),
        ])

        const totalPages = Math.ceil(totalItems / limit)

        return {
            items,
            meta: {
                totalItems,
                itemCount: items.length,
                itemsPerPage: limit,
                totalPages,
                currentPage: page,
            },
        }
    }

    async findOne(id: string): Promise<Company> {
        let companyDetails = null
        try {
            companyDetails = this.prisma.company.findUnique({
                where: {
                    id: id,
                },
                include: { companyUsers: true, jobAdverts: true },
            })
            if (!companyDetails) new NotFoundException('Company not found')
        } catch (error) {
            throw new NotFoundException(error.message)
        }

        return companyDetails
    }

    async update(
        id: string,
        updateCompanyInput: UpdateCompanyInput
    ): Promise<Company> {
        const updatedCompany = await this.prisma.company.update({
            where: {
                id: id,
            },
            data: updateCompanyInput,
        })

        if (updatedCompany) {
            //TODO:: Delete images from stale images table
        }

        return updatedCompany
    }

    async manageFair(id: string, isFairManaged: boolean) {
        const updatedCompany = await this.prisma.company.update({
            where: {
                id: id,
            },
            data: {
                isFairManaged: isFairManaged,
            },
        })

        return updatedCompany
    }

    async remove(id: string) {
        const companyToDelete = this.prisma.company.findUnique({
            where: { id: id },
        })
        if (!companyToDelete) {
            throw new NotFoundException(`Company with ID ${id} does not exist`)
        }
        const deletedStats = await this.getDeletionStats(id)
        const deletedCompany = await this.deleteCompanyAndRelatedData(id)
        const orphanedData = this.verifyNoOrphanedData(id)

        if (!orphanedData) {
            throw new NotFoundException('Orphaned data found')
        }

        return deletedCompany
    }

    async verifyNoOrphanedData(companyId: string) {
        const orphanedData = await this.prisma.$transaction([
            this.prisma.jobAdvert.findMany({
                where: { companyId },
            }),
            this.prisma.stripeSubscription.findMany({
                where: { companyId },
            }),
            this.prisma.userRights.findMany({
                where: { companyId },
            }),
        ])

        return orphanedData.every((data) => data.length === 0)
    }

    isValidCompany(checkCompanyId: string, companyId: string): boolean {
        if (checkCompanyId && checkCompanyId !== companyId) {
            throw new NotFoundException('Invalid company ID')
        }
        const company = this.prisma.company.findUnique({
            where: {
                id: companyId,
            },
        })

        if (!company) throw new NotFoundException('Company not found')

        return !!company
    }

    async isValidCompanyUser(
        checkCompanyUserId: string,
        companyId: string
    ): Promise<boolean> {
        const company = await this.prisma.company.findUnique({
            where: {
                id: companyId,
            },
            include: {
                companyUsers: true,
            },
        })

        const companyUser = company?.companyUsers.find(
            (user) => user.id === checkCompanyUserId
        )

        const isValid =
            !!companyUser || company.companyUserId === checkCompanyUserId

        if (!company) throw new NotFoundException('Company User not found')

        return !!isValid
    }

    async deleteCompanyAndRelatedData(companyId: string) {
        try {
            return await this.prisma.$transaction(
                async (prisma) => {
                    await prisma.jobAction.deleteMany({
                        where: {
                            jobAdvert: {
                                companyId: companyId,
                            },
                        },
                    })

                    await prisma.message.deleteMany({
                        where: {
                            companyUser: {
                                companies: {
                                    some: {
                                        id: companyId,
                                    },
                                },
                            },
                        },
                    })

                    await prisma.chatRoom.deleteMany({
                        where: {
                            jobAction: {
                                jobAdvert: {
                                    companyId: companyId,
                                },
                            },
                        },
                    })

                    await prisma.userRights.deleteMany({
                        where: { companyId },
                    })

                    await prisma.stripeSubscription.deleteMany({
                        where: { companyId },
                    })

                    await prisma.jobAdvert.deleteMany({
                        where: { companyId },
                    })

                    //remove the users via email from firebase
                    const companyUsers = await prisma.companyUser.findMany({
                        where: {
                            companies: {
                                some: {
                                    id: companyId,
                                },
                            },
                        },
                        select: {
                            user: {
                                select: {
                                    firebaseUid: true,
                                },
                            },
                        },
                    })

                    for (const user of companyUsers) {
                        await admin.auth().deleteUser(user.user.firebaseUid)
                    }

                    //Delete companyUsers for the company
                    await prisma.companyUser.deleteMany({
                        where: {
                            companies: {
                                some: {
                                    id: companyId,
                                },
                            },
                        },
                    })

                    await prisma.$executeRaw`
          DELETE FROM "_CompanyToCompanyUser"
          WHERE "A" = ${companyId}::uuid;
        `

                    //Finally delete the company
                    const deletedCompany = await prisma.company.delete({
                        where: { id: companyId },
                    })

                    return {
                        ...deletedCompany,
                    }
                },
                { timeout: 10000 }
            )
        } catch (error) {
            console.error('Deletion error:', error)
            throw new Error(`Failed to delete company: ${error.message}`)
        }
    }

    async getDeletionStats(companyId: string) {
        const stats = await this.prisma.$transaction([
            this.prisma.jobAction.count({
                where: {
                    jobAdvert: {
                        companyId: companyId,
                    },
                },
            }),
            this.prisma.message.count({
                where: {
                    companyUser: {
                        companies: {
                            some: {
                                id: companyId,
                            },
                        },
                    },
                },
            }),
            this.prisma.userRights.count({
                where: { companyId },
            }),
            this.prisma.stripeSubscription.count({
                where: { companyId },
            }),
            this.prisma.jobAdvert.count({
                where: { companyId },
            }),
        ])

        return {
            jobActions: stats[0],
            messages: stats[1],
            userRights: stats[2],
            subscriptions: stats[3],
            jobAdverts: stats[4],
        }
    }
}
